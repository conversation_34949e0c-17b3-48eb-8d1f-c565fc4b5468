"use client";

import { motion } from "framer-motion";
import { Head<PERSON> } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { FiClock, FiMapPin, FiPhone, FiMail } from "react-icons/fi";

export default function HoursInfoPage() {
  const hours = [
    { day: "Monday", time: "5:00 PM - 10:00 PM" },
    { day: "Tuesday", time: "5:00 PM - 10:00 PM" },
    { day: "Wednesday", time: "5:00 PM - 11:00 PM" },
    { day: "Thursday", time: "5:00 PM - 11:00 PM" },
    { day: "Friday", time: "5:00 PM - 12:00 AM" },
    { day: "Saturday", time: "4:00 PM - 12:00 AM" },
    { day: "Sunday", time: "4:00 PM - 10:00 PM" },
  ];

  const contactInfo = [
    {
      icon: FiMapPin,
      title: "Address",
      details: ["123 Culinary Street", "Downtown District", "City, State 12345"]
    },
    {
      icon: FiPhone,
      title: "Phone",
      details: ["(*************", "Reservations: (*************"]
    },
    {
      icon: FiMail,
      title: "Email",
      details: ["<EMAIL>", "<EMAIL>"]
    }
  ];

  return (
    <>
      <Header />
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 px-4 bg-[#191919]">
          <div className="container mx-auto text-center">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl lg:text-6xl font-playfair font-bold text-white mb-6"
            >
              Hours & Information
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-gray-300 text-lg md:text-xl max-w-3xl mx-auto mb-12"
            >
              Find all the information you need to plan your visit, including our hours, 
              location, and contact details.
            </motion.p>
          </div>
        </section>

        {/* Hours and Contact Info */}
        <section className="py-20 px-4 bg-[#F5F5F0]">
          <div className="container mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
              {/* Hours */}
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="bg-white rounded-lg shadow-lg p-8"
              >
                <div className="flex items-center mb-6">
                  <FiClock className="text-[#D4AF37] text-2xl mr-3" />
                  <h2 className="text-2xl font-playfair font-bold text-[#2C2C2C]">
                    Operating Hours
                  </h2>
                </div>
                <div className="space-y-4">
                  {hours.map((item, index) => (
                    <div key={item.day} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="font-medium text-[#2C2C2C]">{item.day}</span>
                      <span className="text-[#666666]">{item.time}</span>
                    </div>
                  ))}
                </div>
                <div className="mt-6 p-4 bg-[#D4AF37]/10 rounded-lg">
                  <p className="text-[#666666] text-sm">
                    <strong>Note:</strong> Hours may vary during holidays. Please call ahead to confirm.
                  </p>
                </div>
              </motion.div>

              {/* Contact Information */}
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="space-y-8"
              >
                {contactInfo.map((info, index) => (
                  <div key={info.title} className="bg-white rounded-lg shadow-lg p-6">
                    <div className="flex items-center mb-4">
                      <info.icon className="text-[#D4AF37] text-xl mr-3" />
                      <h3 className="text-xl font-playfair font-bold text-[#2C2C2C]">
                        {info.title}
                      </h3>
                    </div>
                    <div className="space-y-2">
                      {info.details.map((detail, detailIndex) => (
                        <p key={detailIndex} className="text-[#666666]">
                          {detail}
                        </p>
                      ))}
                    </div>
                  </div>
                ))}
              </motion.div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
