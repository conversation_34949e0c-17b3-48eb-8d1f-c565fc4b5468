import Link from 'next/link';
import Image from 'next/image';
import { MapPin, Phone } from 'lucide-react';

export const Footer = () => {
  return (
    <footer className="relative bg-[#1B2951] overflow-hidden">
      {/* Background Food Image */}
      <div className="absolute inset-0 z-0">
        <Image
          src="/assets/dishes/risotto-ai-funghi.jpg"
          alt="Delicious food"
          fill
          className="object-cover opacity-30"
          sizes="100vw"
        />
        <div className="absolute inset-0 bg-[#1B2951]/80"></div>
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-center">

          {/* Left Section - Main Tagline */}
          <div className="lg:col-span-1">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-playfair font-bold text-white leading-tight">
              Timeless recipes to<br />
              savor & enjoy
            </h2>
          </div>

          {/* Center Section - Contact Information */}
          <div className="lg:col-span-1 space-y-6">
            {/* Address */}
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-[#D4AF37] flex items-center justify-center flex-shrink-0 mt-1">
                <MapPin className="w-4 h-4 text-white" />
              </div>
              <div className="text-gray-300">
                <p className="text-sm">256 North Neusvill Avenue</p>
                <p className="text-sm">Neusvill, PA 19302</p>
              </div>
            </div>

            {/* Phone & Email */}
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-[#D4AF37] flex items-center justify-center flex-shrink-0 mt-1">
                <Phone className="w-4 h-4 text-white" />
              </div>
              <div className="text-gray-300">
                <p className="text-sm"><EMAIL></p>
                <p className="text-sm">+123 (456) 789 00</p>
              </div>
            </div>
          </div>

          {/* Right Section - Logo and Footer Links */}
          <div className="lg:col-span-1 flex flex-col items-center lg:items-end space-y-8">
            {/* Bamzi Logo */}
            <div className="text-center lg:text-right">
              <div className="flex items-center justify-center lg:justify-end">
                <span className="text-2xl font-playfair font-bold text-[#D4AF37]">Bamzi</span>
              </div>
            </div>

            {/* Footer Links */}
            <div className="flex flex-wrap justify-center lg:justify-end gap-6 text-sm text-gray-300">
              <Link href="/privacy" className="hover:text-[#D4AF37] transition-colors">
                PRIVACY POLICY
              </Link>
              <Link href="/careers" className="hover:text-[#D4AF37] transition-colors">
                CAREERS
              </Link>
              <Link href="/faq" className="hover:text-[#D4AF37] transition-colors">
                FAQ
              </Link>
            </div>
          </div>
        </div>

        {/* Bottom Copyright */}
        <div className="mt-16 pt-8 border-t border-white/10 text-center">
          <p className="text-sm text-gray-400">
            Copyright & design by <span className="text-[#D4AF37]">@FramerDevs</span>
          </p>
        </div>
      </div>
    </footer>
  );
};
