import Link from 'next/link';
import Image from 'next/image';
import { MapPin, Phone } from 'lucide-react';

export const Footer = () => {
  return (
    <footer className="relative bg-[#1a3a3a] overflow-hidden min-h-[400px]">
      {/* Ramen Bowl Image - Positioned in top-right */}
      <div className="absolute top-0 right-0 w-1/2 h-full z-0">
        <Image
          src="/assets/dishes/dishemenu.jpg"
          alt="Delicious ramen bowl"
          fill
          className="object-cover object-center opacity-80"
          sizes="50vw"
        />
      </div>

      {/* Dark overlay for better text readability */}
      <div className="absolute inset-0 bg-gradient-to-r from-[#1a3a3a] via-[#1a3a3a]/90 to-[#1a3a3a]/60 z-5"></div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">

          {/* Left Section - Main Content */}
          <div className="space-y-8">
            {/* Main Tagline */}
            <div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-playfair font-normal text-white leading-tight">
                Timeless recipes to<br />
                savor & enjoy
              </h2>
            </div>

            {/* Contact Information */}
            <div className="space-y-4">
              {/* Address */}
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-[#FF6B35] flex items-center justify-center flex-shrink-0 mt-0.5">
                  <MapPin className="w-3 h-3 text-white" />
                </div>
                <div className="text-gray-200">
                  <p className="text-sm">256 North Neusvill Avenue</p>
                  <p className="text-sm">Neusvill, PA 19302</p>
                </div>
              </div>

              {/* Phone & Email */}
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-[#FF6B35] flex items-center justify-center flex-shrink-0 mt-0.5">
                  <Phone className="w-3 h-3 text-white" />
                </div>
                <div className="text-gray-200">
                  <p className="text-sm"><EMAIL></p>
                  <p className="text-sm">+123 (456) 789 00</p>
                </div>
              </div>
            </div>
          </div>

          {/* Right Section - Logo (positioned over the image area) */}
          <div className="flex flex-col items-end justify-end h-full pt-8 lg:pt-16">
            {/* Restaurant Logo */}
            <div className="mb-8">
              <div className="flex items-center justify-center lg:justify-end">
                <Image
                  src="/assets/WHITEBRASSERIE.svg"
                  alt="Restaurant Logo"
                  width={120}
                  height={60}
                  className="h-12 w-auto"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Footer Links */}
        <div className="mt-12 pt-6 border-t border-white/20">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            {/* Copyright */}
            <p className="text-xs text-gray-400">
              Copyright & design by <span className="text-[#FF6B35]">@FramerDevs</span>
            </p>

            {/* Footer Links */}
            <div className="flex gap-4 text-xs text-gray-300">
              <Link href="/privacy" className="hover:text-[#FF6B35] transition-colors">
                PRIVACY POLICY
              </Link>
              <Link href="/careers" className="hover:text-[#FF6B35] transition-colors">
                CAREERS
              </Link>
              <Link href="/faq" className="hover:text-[#FF6B35] transition-colors">
                FAQ
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
