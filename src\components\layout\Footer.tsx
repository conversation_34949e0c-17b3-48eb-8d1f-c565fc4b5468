import Link from 'next/link';
import { Facebook, Instagram, Twitter } from 'lucide-react';

const socialLinks = [
  { icon: Facebook, href: '#' },
  { icon: Instagram, href: '#' },
  { icon: Twitter, href: '#' },
];

const quickLinks = [
  { label: 'About Us', href: '#' },
  { label: 'Menu', href: '#' },
  { label: 'Services', href: '#' },
  { label: 'Contact', href: '#' },
];

const contactInfo = [
  { label: 'Email', value: '<EMAIL>' },
  { label: 'Phone', value: '****** 567 890' },
  { label: 'Address', value: '123 Food Street, NY 10001' },
];

export const Footer = () => {
  return (
    <footer className="bg-[#1a1a1a] pt-16 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
          {/* Brand Column */}
          <div>
            <Link href="/" className="text-2xl font-bold mb-4 inline-block">
              Foodkh
            </Link>
            <p className="text-gray-400 mb-6">
              Experience the perfect blend of taste and nutrition with our 
              carefully crafted dishes.
            </p>
            <div className="flex gap-4">
              {socialLinks.map((social, index) => {
                const Icon = social.icon;
                return (
                  <a
                    key={index}
                    href={social.href}
                    className="w-10 h-10 rounded-full bg-white/5 flex items-center justify-center hover:bg-[#FDB92A] transition-colors"
                  >
                    <Icon className="w-5 h-5" />
                  </a>
                );
              })}
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Quick Links</h3>
            <ul className="space-y-4">
              {quickLinks.map((link) => (
                <li key={link.label}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-[#FDB92A] transition-colors"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Contact</h3>
            <ul className="space-y-4">
              {contactInfo.map((info) => (
                <li key={info.label} className="text-gray-400">
                  <span className="block text-sm">{info.label}</span>
                  <span className="block mt-1">{info.value}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Opening Hours */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Opening Hours</h3>
            <ul className="space-y-4 text-gray-400">
              <li>
                <span className="block">Monday - Friday</span>
                <span className="block text-[#FDB92A]">9:00 AM - 10:00 PM</span>
              </li>
              <li>
                <span className="block">Saturday - Sunday</span>
                <span className="block text-[#FDB92A]">10:00 AM - 11:00 PM</span>
              </li>
            </ul>
          </div>
        </div>

        {/* Copyright */}
        <div className="border-t border-white/10 pt-8 text-center text-gray-400">
          <p>© {new Date().getFullYear()} Foodkh. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};
