"use client";

import { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { BookingForm } from "../forms/BookingForm";

export const Hero = () => {
  const [formData, setFormData] = useState({
    restaurant: "",
    seats: "",
    date: "",
    time: "",
  });

  const [videoError, setVideoError] = useState(false); // Using Cloudinary video URL
  const videoRef = useRef<HTMLVideoElement>(null);

  // Handle video loading and error states
  useEffect(() => {
    const video = videoRef.current;
    if (video) {
      const handleError = (e: Event) => {
        console.error("Background video failed to load:", e);
        console.log("Video element:", video);
        setVideoError(true);
      };

      const handleCanPlay = () => {
        console.log("Video can play - loading successful");
        setVideoError(false);
      };

      const handleLoadStart = () => {
        console.log("Video loading started");
      };

      video.addEventListener("error", handleError);
      video.addEventListener("canplay", handleCanPlay);
      video.addEventListener("loadstart", handleLoadStart);

      return () => {
        video.removeEventListener("error", handleError);
        video.removeEventListener("canplay", handleCanPlay);
        video.removeEventListener("loadstart", handleLoadStart);
      };
    }
  }, []);

  const handleFormChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log("Booking submitted:", formData);
    // Reset form
    setFormData({
      restaurant: "",
      seats: "",
      date: "",
      time: "",
    });
  };

  return (
    <section className="relative min-h-screen flex items-center pt-20">
      {/* Background Video */}
      <div className="absolute inset-0">
        {!videoError ? (
          <video
            ref={videoRef}
            autoPlay
            muted
            loop
            playsInline
            className="absolute inset-0 w-full h-full object-cover"
            poster="/assets/bg.png"
            preload="metadata"
          >
            <source src="https://res.cloudinary.com/dviwkw86f/video/upload/v1748756008/herovideo_s5rjoj.mp4" type="video/mp4" />
          </video>
        ) : (
          /* Fallback background image when video fails */
          <div
            className="absolute inset-0 w-full h-full bg-cover bg-center"
            style={{ backgroundImage: 'url(/assets/bg.png)' }}
          />
        )}
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/30 to-transparent" />
      </div>

      <div className="relative container mx-auto px-4 h-full flex items-start pt-16 pb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 w-full">
          {/* Left Side - Text Content */}
          <div className="relative h-full w-full max-w-2xl z-10 text-white">
            {/* Text Content - Top Left */}
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="absolute top-0 left-0 p-4 text-2xl md:text-3xl lg:text-4xl font-bold leading-tight font-serif"
            >
              Authentic foods,
              <br />
              where tradition
              <br />
              meets craft
            </motion.h1>

            {/* Video Controls or Additional Content - Bottom Left */}
            <div className="absolute bottom-0 left-0 p-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.8 }}
                className="text-white/80 text-sm"
              >
                Experience our atmosphere
              </motion.div>
            </div>
          </div>

          {/* Right Side - Booking Form */}
          <BookingForm
            formData={formData}
            onFormChange={handleFormChange}
            onSubmit={handleSubmit}
          />
        </div>
      </div>
    </section>
  );
};
