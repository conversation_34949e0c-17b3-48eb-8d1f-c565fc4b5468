"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from "framer-motion";
import { BookingForm } from "../forms/BookingForm";

interface BackgroundImage {
  id: number;
  src: string;
  alt: string;
}

export const Hero = () => {
  const [formData, setFormData] = useState({
    restaurant: "",
    seats: "",
    date: "",
    time: "",
  });

  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const backgroundImages: BackgroundImage[] = [
    { id: 1, src: "/assets/bg.png", alt: "Restaurant Interior" },
    { id: 2, src: "/assets/gallery01.png", alt: "Delicious Food" },
    { id: 3, src: "/assets/gallery02.png", alt: "Restaurant Ambiance" },
  ];

  useEffect(() => {
    // Auto-rotate images every 5 seconds
    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === backgroundImages.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, [backgroundImages.length]);

  const handleFormChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleThumbnailClick = (index: number) => {
    setCurrentImageIndex(index);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log("Booking submitted:", formData);
    // Reset form
    setFormData({
      restaurant: "",
      seats: "",
      date: "",
      time: "",
    });
  };

  return (
    <section className="relative min-h-screen flex items-center pt-20">
      {/* Background Images with Animation */}
      <div className="absolute inset-0">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentImageIndex}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }} 
            transition={{ duration: 0.8 }}
            className="absolute inset-0"
          >
            <Image
              src={backgroundImages[currentImageIndex].src}
              alt={backgroundImages[currentImageIndex].alt}
              fill
              className="object-cover object-center"
              priority
              quality={100}
            />
          </motion.div>
        </AnimatePresence>
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 via-black/30 to-transparent" />
      </div>

      <div className="relative container mx-auto px-4 h-full flex items-start pt-16 pb-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 w-full">
          {/* Left Side - Text Content */}
          <div className="relative h-full w-full max-w-2xl z-10 text-white">
            {/* Text Content - Top Left */}
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="absolute top-0 left-0 p-4 text-2xl md:text-3xl lg:text-4xl font-bold leading-tight font-serif"
            >
              Authentic foods,
              <br />
              where tradition
              <br />
              meets craft
            </motion.h1>

            {/* Small Images Carousel - Bottom Left */}
            <div className="absolute bottom-0 left-0 p-4 flex gap-4">
              {backgroundImages.map((image, index) => (
                <motion.div
                  key={image.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className={`w-20 h-20 md:w-24 md:h-24 rounded-lg overflow-hidden transform transition-all duration-300 cursor-pointer ${
                    currentImageIndex === index
                      ? "ring-2 ring-amber-400 scale-105"
                      : "opacity-70 hover:opacity-100"
                  }`}
                  onClick={() => handleThumbnailClick(index)}
                >
                  <Image
                    src={image.src}
                    alt={image.alt}
                    width={100}
                    height={100}
                    className="w-full h-full object-cover"
                  />
                </motion.div>
              ))}
            </div>
          </div>

          {/* Right Side - Booking Form */}
          <BookingForm
            formData={formData}
            onFormChange={handleFormChange}
            onSubmit={handleSubmit}
          />
        </div>
      </div>
    </section>
  );
};
