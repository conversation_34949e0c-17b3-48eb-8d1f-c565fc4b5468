import Link from 'next/link';

interface NavLink {
  label: string;
  href: string;
}

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  navLinks: NavLink[];
  language: string;
  onLanguageChange: (lang: string) => void;
  onSearchClick: () => void;
}

export const MobileMenu = ({
  isOpen,
  onClose,
  navLinks,
  language,
  onLanguageChange,
  onSearchClick
}: MobileMenuProps) => {
  if (!isOpen) return null;

  return (
    <div className="lg:hidden mt-4 pb-4 bg-black/90 backdrop-blur-md p-4 rounded-lg">
      <nav className="flex flex-col space-y-3">
        {navLinks.map((link) => (
          <Link
            key={link.label}
            href={link.href}
            className="text-white/90 hover:text-amber-400 transition-colors py-2 block text-xs uppercase tracking-widest font-medium"
            onClick={onClose}
          >
            {link.label}
          </Link>
        ))}
      </nav>

      <div className="mt-4 pt-4 border-t border-white/10">
        <div className="flex items-center justify-between">
          <button
            onClick={onSearchClick}
            className="text-white/90 hover:text-amber-400 transition-colors p-2"
            aria-label="Search"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </button>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onLanguageChange(language === 'EN' ? 'ID' : 'EN')}
              className="text-white/90 hover:text-amber-400 transition-colors text-sm font-medium px-3 py-1 rounded-full border border-white/20 hover:border-amber-400/50"
            >
              {language}
            </button>
            
            <Link
              href="/contact"
              className="bg-amber-500 hover:bg-amber-600 text-white text-sm font-medium px-4 py-2 rounded-full transition-colors"
              onClick={onClose}
            >
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};
