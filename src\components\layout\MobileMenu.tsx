"use client";
import { useEffect, useCallback } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";

interface NavLink {
  label: string;
  href: string;
}

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
  navLinks: NavLink[];
  language: string;
  onLanguageChange: () => void;
}

export const MobileMenu = ({
  isOpen,
  onClose,
  navLinks,
  language,
  onLanguageChange,
}: MobileMenuProps) => {
  const pathname = usePathname();

  // Close mobile menu when pathname changes
  useEffect(() => {
    if (isOpen) {
      onClose();
    }
  }, [pathname, isOpen, onClose]);

  // Close mobile menu on escape key and manage body overflow
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  const handleLinkClick = useCallback(() => {
    onClose();
  }, [onClose]);

  const handleBackdropClick = useCallback(() => {
    onClose();
  }, [onClose]);

  return (
    <div
      className={`lg:hidden fixed inset-0 z-40 transition-all duration-300 ease-in-out ${
        isOpen
          ? "opacity-100 pointer-events-auto"
          : "opacity-0 pointer-events-none"
      }`}
    >
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/60 backdrop-blur-sm"
        onClick={handleBackdropClick}
        aria-hidden="true"
      />

      {/* Menu Panel */}
      <div
        id="mobile-menu"
        className={`absolute top-16 left-0 right-0 bg-black/90 backdrop-blur-md border-t border-white/10 shadow-2xl transform transition-all duration-300 ease-in-out ${
          isOpen
            ? "translate-y-0 opacity-100"
            : "-translate-y-4 opacity-0"
        }`}
        role="dialog"
        aria-modal="true"
        aria-labelledby="mobile-menu-heading"
      >
        <div className="px-4 py-6 space-y-1">
          <h2 id="mobile-menu-heading" className="sr-only">
            Navigation Menu
          </h2>

          {/* Navigation Links */}
          <nav className="space-y-1" role="navigation">
            {navLinks.map((link, index) => (
              <Link
                key={link.href}
                href={link.href}
                onClick={handleLinkClick}
                className={`block px-4 py-3 text-base font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-black/90 ${
                  pathname === link.href
                    ? "text-[#D4AF37] bg-white/20 font-semibold"
                    : "text-white hover:bg-white/10 hover:text-[#D4AF37]"
                }`}
                style={{
                  animationDelay: `${index * 50}ms`,
                  animation: isOpen
                    ? "slideInFromTop 0.3s ease-out forwards"
                    : "none",
                }}
                aria-current={pathname === link.href ? "page" : undefined}
              >
                {link.label}
              </Link>
            ))}
          </nav>

          {/* Language & Contact Section */}
          <div className="pt-6 mt-6 border-t border-white/20 space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-white text-sm">🌐</span>
                <button
                  onClick={onLanguageChange}
                  className="text-white text-sm font-medium hover:text-[#D4AF37] transition-colors duration-200 px-3 py-1 rounded border border-white/20 hover:border-[#D4AF37]/50"
                >
                  {language}
                </button>
              </div>
            </div>
            <Link
              href="/contact"
              onClick={handleLinkClick}
              className="block text-center py-3 px-4 bg-[#D4AF37] text-white font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-black/90 hover:bg-[#D4AF37]/90 hover:shadow-lg"
            >
              CONTACT US
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};
