# Background Video Setup Instructions

## Required Video Files

To complete the Hero component background video setup, you need to add the following video files to the `/public/assets/` directory:

### Primary Video File
- **Filename**: `restaurant-background.mp4`
- **Format**: MP4 (H.264 codec recommended)
- **Recommended specs**:
  - Resolution: 1920x1080 (Full HD) or higher
  - Duration: 10-30 seconds (will loop automatically)
  - Frame rate: 24-30 fps
  - Bitrate: 2-5 Mbps for good quality/performance balance

### Alternative Format (Optional but Recommended)
- **Filename**: `restaurant-background.webm`
- **Format**: WebM (VP9 codec recommended)
- **Purpose**: Better compression and browser support

## Video Content Suggestions

For a restaurant/bistro theme, consider videos showing:
- Elegant restaurant interior with ambient lighting
- Chef preparing dishes in the kitchen
- Food presentation and plating
- Restaurant atmosphere during service
- Close-ups of signature dishes

## Technical Requirements

### Video Optimization
- Keep file size under 10MB for good loading performance
- Ensure the video works well when muted (no important audio content)
- Test on mobile devices for performance
- Consider using a video compression tool to optimize file size

### Browser Compatibility
- MP4 format supports: Chrome, Firefox, Safari, Edge
- WebM format supports: Chrome, Firefox, Opera
- Fallback image (`/assets/bg.png`) will show if video fails to load

## Implementation Notes

The Hero component is now configured to:
- ✅ Auto-play the video on page load
- ✅ Loop continuously
- ✅ Remain muted by default
- ✅ Cover the full hero section area
- ✅ Maintain responsive behavior
- ✅ Show fallback image if video fails
- ✅ Preserve all existing functionality (booking form, text content, animations)

## File Placement

Place your video files in:
```
public/
  assets/
    restaurant-background.mp4  ← Add this file
    restaurant-background.webm ← Add this file (optional)
    bg.png                     ← Already exists (fallback image)
```

## Testing

After adding the video files:
1. Refresh the page to see the background video
2. Test on different devices and browsers
3. Verify the video loops smoothly
4. Check that the booking form and text content remain functional
5. Ensure the fallback image appears if video fails to load

## Performance Tips

- Use video compression tools like HandBrake or FFmpeg
- Consider creating different video sizes for mobile vs desktop
- Monitor loading times and adjust video quality if needed
- Test with slow internet connections to ensure good user experience
