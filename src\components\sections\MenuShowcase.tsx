'use client';

import { motion } from 'framer-motion';
import { MenuItemCard } from '../ui/MenuItemCard';
import { Button } from '../ui/Button';

const FEATURED_MENU = [
  {
    id: '1',
    name: 'Sea Bass Fillet',
    price: 32,
    image: '/assets/dishes/sea-bass-1.webp',
    description: 'Fresh sea bass fillet with herbs and lemon butter sauce'
  },
  {
    id: '2',
    name: 'Spicy Rigatoni',
    price: 25,
    image: '/assets/dishes/Spicy-Rigatoni_THUMB.jpg',
    description: 'Authentic Italian rigatoni with spicy tomato sauce'
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON> Steak',
    price: 48,
    image: '/assets/dishes/<EMAIL>',
    description: 'Premium ribeye with bourbon caramelized onions'
  },
  {
    id: '4',
    name: 'Risotto ai Funghi',
    price: 28,
    image: '/assets/dishes/risotto-ai-funghi.jpg',
    description: 'Creamy mushroom risotto with parmesan and truffle oil'
  },
  {
    id: '5',
    name: 'Veal Osso Bucco',
    price: 45,
    image: '/assets/dishes/veal-osso-bucco-cropped.jpg',
    description: 'Slow-braised veal shank with saffron risotto'
  },
  {
    id: '6',
    name: 'Chef\'s Special',
    price: 35,
    image: '/assets/dishes/dishemenu.jpg',
    description: 'Daily special crafted by our executive chef'
  },
  {
    id: '7',
    name: 'Château Latour Wine',
    price: 180,
    image: '/assets/wines/Château Latour.webp',
    description: 'Premium French wine from Bordeaux region'
  },
  {
    id: '8',
    name: 'Ramonet Chardonnay',
    price: 120,
    image: '/assets/wines/Domaine Jean Claude Ramonet.png',
    description: 'Elegant Burgundy white wine with mineral notes'
  },
];

export const MenuShowcase = () => {
  return (
    <section className="py-20 px-4">
      <div className="container mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl md:text-4xl font-playfair font-bold mb-4">
            Choose Our Menu
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Explore our carefully curated menu featuring dishes that combine 
            exquisite flavors with nutritious ingredients
          </p>
        </motion.div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {FEATURED_MENU.map((item) => (
            <MenuItemCard key={item.id} {...item} />
          ))}
        </div>

        <div className="text-center">
          <Button variant="primary" size="lg">
            View All Menu
          </Button>
        </div>
      </div>
    </section>
  );
};
