"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { Button } from "@/components/ui/Button";

export default function EventsPage() {
  const events = [
    {
      title: "Wine Tasting Evening",
      date: "Every Friday",
      time: "7:00 PM - 10:00 PM",
      description: "Join us for an exclusive wine tasting experience featuring carefully selected wines from around the world.",
      image: "/food-background.jpg"
    },
    {
      title: "Chef's Special Dinner",
      date: "Last Saturday of the Month",
      time: "6:00 PM - 9:00 PM", 
      description: "Experience our chef's signature dishes in a special multi-course dinner event.",
      image: "/restaurant-interior.jpg"
    },
    {
      title: "Live Jazz Night",
      date: "Every Wednesday",
      time: "8:00 PM - 11:00 PM",
      description: "Enjoy live jazz music while dining in our elegant atmosphere.",
      image: "/tables-bg.jpg"
    }
  ];

  return (
    <>
      <Header />
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 px-4 bg-[#191919]">
          <div className="container mx-auto text-center">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl lg:text-6xl font-playfair font-bold text-white mb-6"
            >
              Events & Experiences
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-gray-300 text-lg md:text-xl max-w-3xl mx-auto mb-12"
            >
              Join us for special events, tastings, and unique dining experiences that celebrate 
              the art of fine cuisine and hospitality.
            </motion.p>
          </div>
        </section>

        {/* Events Grid */}
        <section className="py-20 px-4 bg-[#F5F5F0]">
          <div className="container mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {events.map((event, index) => (
                <motion.div
                  key={event.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-lg shadow-lg overflow-hidden"
                >
                  <div className="relative h-48">
                    <Image
                      src={event.image}
                      alt={event.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-playfair font-bold text-[#2C2C2C] mb-2">
                      {event.title}
                    </h3>
                    <div className="text-[#D4AF37] font-medium mb-1">{event.date}</div>
                    <div className="text-[#666666] text-sm mb-4">{event.time}</div>
                    <p className="text-[#666666] mb-6">{event.description}</p>
                    <Button
                      variant="primary"
                      className="bg-[#D4AF37] text-white hover:bg-[#D4AF37]/90 w-full"
                    >
                      Learn More
                    </Button>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
