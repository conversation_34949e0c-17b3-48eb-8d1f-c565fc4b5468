"use client";
import { useState, useCallback, useEffect } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Image from "next/image";

interface NavLink {
  label: string;
  href: string;
}

interface NavbarProps {
  className?: string;
}

export const Navbar = ({ className = "" }: NavbarProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const pathname = usePathname();

  const navLinks: NavLink[] = [
    { label: "Menu", href: "/menu" },
    { label: "About", href: "/about" },
    { label: "Events", href: "/events" },
    { label: "Hours & Info", href: "/hours-info" },
  ];

  // Close mobile menu when pathname changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [pathname]);

  // Close mobile menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isMenuOpen) {
        setIsMenuOpen(false);
      }
    };

    if (isMenuOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isMenuOpen]);

  const toggleMenu = useCallback(() => {
    setIsMenuOpen((prev) => !prev);
  }, []);

  const handleMobileMenuClose = useCallback(() => {
    setIsMenuOpen(false);
  }, []);

  const handleLinkClick = useCallback(() => {
    setIsMenuOpen(false);
  }, []);

  return (
    <nav
      className={`w-full fixed top-0 left-0 z-50 bg-[#1B2951] shadow-lg transition-all duration-300 ${className}`}
    >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo - Left */}
          <div className="flex-shrink-0">
            <Link
              href="/"
              className="flex items-center group focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-[#1B2951] rounded-lg p-1 transition-all duration-200"
              aria-label="Brasserie Home"
            >
              <Image
                src="/assets/BLUBRASSERIE.svg"
                alt="Logo"
                width={40}
                height={40}
              ></Image>
            </Link>
          </div>

          {/* Center Navigation Links */}
          <div className="hidden lg:flex items-center space-x-1">
            {navLinks.map((link) => (
              <Link
                key={link.label}
                href={link.href}
                className={`relative px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-[#1B2951] ${
                  pathname === link.href
                    ? "text-[#D4AF37] bg-white/10 font-semibold"
                    : "text-white hover:text-[#D4AF37] hover:bg-white/5"
                }`}
                aria-current={pathname === link.href ? "page" : undefined}
              >
                {link.label}
                {pathname === link.href && (
                  <span className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-[#D4AF37] rounded-full"></span>
                )}
              </Link>
            ))}
          </div>

          {/* Right Section - Action Buttons */}
          <div className="flex items-center space-x-3">
            {/* Private Events Button */}
            <Link
              href="/private-events"
              className={`hidden lg:inline-flex items-center border border-white text-white text-sm font-medium px-4 py-2 rounded-md transition-all duration-200 hover:bg-white hover:text-[#1B2951] focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-[#1B2951] ${
                pathname === "/private-events" ? "bg-white text-[#1B2951]" : ""
              }`}
              aria-current={pathname === "/private-events" ? "page" : undefined}
            >
              Private Events
            </Link>

            {/* Reserve Table Button */}
            <Link
              href="/reserve"
              className={`hidden lg:inline-flex items-center bg-[#D4AF37] text-white text-sm font-semibold px-6 py-2 rounded-md transition-all duration-200 hover:bg-[#D4AF37]/90 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-[#1B2951] ${
                pathname === "/reserve" ? "bg-[#D4AF37]/90 shadow-lg" : ""
              }`}
              aria-current={pathname === "/reserve" ? "page" : undefined}
            >
              Reserve Table
            </Link>

            {/* Mobile menu button */}
            <button
              onClick={toggleMenu}
              className="lg:hidden relative p-2 text-white hover:text-[#D4AF37] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-[#1B2951] rounded-md"
              aria-label={isMenuOpen ? "Close menu" : "Open menu"}
              aria-expanded={isMenuOpen}
              aria-controls="mobile-menu"
            >
              <span className="sr-only">
                {isMenuOpen ? "Close menu" : "Open menu"}
              </span>
              <div className="w-6 h-6 relative">
                <span
                  className={`absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out ${
                    isMenuOpen ? "rotate-45 translate-y-0" : "-translate-y-2"
                  }`}
                />
                <span
                  className={`absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out ${
                    isMenuOpen ? "opacity-0" : "opacity-100"
                  }`}
                />
                <span
                  className={`absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out ${
                    isMenuOpen ? "-rotate-45 translate-y-0" : "translate-y-2"
                  }`}
                />
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div
        className={`lg:hidden fixed inset-0 z-40 transition-all duration-300 ease-in-out ${
          isMenuOpen
            ? "opacity-100 pointer-events-auto"
            : "opacity-0 pointer-events-none"
        }`}
      >
        {/* Backdrop */}
        <div
          className="absolute inset-0 bg-black/60 backdrop-blur-sm"
          onClick={handleMobileMenuClose}
          aria-hidden="true"
        />

        {/* Menu Panel */}
        <div
          id="mobile-menu"
          className={`absolute top-16 left-0 right-0 bg-[#1B2951] border-t border-white/10 shadow-2xl transform transition-all duration-300 ease-in-out ${
            isMenuOpen
              ? "translate-y-0 opacity-100"
              : "-translate-y-4 opacity-0"
          }`}
          role="dialog"
          aria-modal="true"
          aria-labelledby="mobile-menu-heading"
        >
          <div className="px-4 py-6 space-y-1">
            <h2 id="mobile-menu-heading" className="sr-only">
              Navigation Menu
            </h2>

            {/* Navigation Links */}
            <nav className="space-y-1" role="navigation">
              {navLinks.map((link, index) => (
                <Link
                  key={link.href}
                  href={link.href}
                  onClick={handleLinkClick}
                  className={`block px-4 py-3 text-base font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-[#1B2951] ${
                    pathname === link.href
                      ? "text-[#D4AF37] bg-white/20 font-semibold"
                      : "text-white hover:bg-white/10 hover:text-[#D4AF37]"
                  }`}
                  style={{
                    animationDelay: `${index * 50}ms`,
                    animation: isMenuOpen
                      ? "slideInFromTop 0.3s ease-out forwards"
                      : "none",
                  }}
                  aria-current={pathname === link.href ? "page" : undefined}
                >
                  {link.label}
                </Link>
              ))}
            </nav>

            {/* Action Buttons */}
            <div className="pt-6 mt-6 border-t border-white/20 space-y-3">
              <Link
                href="/private-events"
                onClick={handleLinkClick}
                className={`block text-center py-3 px-4 border border-white text-white font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-[#1B2951] ${
                  pathname === "/private-events"
                    ? "bg-white text-[#1B2951]"
                    : "hover:bg-white hover:text-[#1B2951]"
                }`}
                aria-current={
                  pathname === "/private-events" ? "page" : undefined
                }
              >
                Private Events
              </Link>
              <Link
                href="/reserve"
                onClick={handleLinkClick}
                className={`block text-center py-3 px-4 bg-[#D4AF37] text-white font-semibold rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-[#1B2951] ${
                  pathname === "/reserve"
                    ? "bg-[#D4AF37]/90 shadow-lg"
                    : "hover:bg-[#D4AF37]/90 hover:shadow-lg"
                }`}
                aria-current={pathname === "/reserve" ? "page" : undefined}
              >
                Reserve Table
              </Link>
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
};
