"use client";
import { useState, useCallback } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Image from "next/image";
import { MobileMenu } from "./MobileMenu";

interface NavLink {
  label: string;
  href: string;
}

interface NavbarProps {
  className?: string;
}

export const Navbar = ({ className = "" }: NavbarProps) => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [language, setLanguage] = useState("EN");
  const pathname = usePathname();

  const navLinks: NavLink[] = [
    { label: "Home", href: "/" },
    { label: "About us", href: "/about" },
    { label: "Menu", href: "/menu" },
    { label: "Hours & Info", href: "/hours-info" },
  ];

  const toggleMenu = useCallback(() => {
    setIsMenuOpen((prev) => !prev);
  }, []);

  const handleMobileMenuClose = useCallback(() => {
    setIsMenuOpen(false);
  }, []);

  const toggleLanguage = useCallback(() => {
    setLanguage((prev) => (prev === "EN" ? "ID" : "EN"));
  }, []);

  return (
    <nav
      className={`w-full fixed top-0 left-0 py-4 z-50 bg-black/80 backdrop-blur-none transition-all duration-300 ${className}`}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo - Left */}
          <div className="flex-shrink-0">
            <Link
              href="/"
              className="flex items-center group focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-black/80 rounded-lg p-1 transition-all duration-200"
              aria-label="Restaurant Home"
            >
              <Image
                src="/assets/BLUBRASSERIE.svg"
                alt="Restaurant Logo"
                width={100}
                height={100}
                className="h-16 "
              />
            </Link>
          </div>

          {/* Center Navigation Links */}
          <div className="hidden lg:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.label}
                href={link.href}
                className={`relative px-2 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-black/80 ${
                  pathname === link.href
                    ? "text-[#D4AF37] font-semibold"
                    : "text-white hover:text-[#D4AF37]"
                }`}
                aria-current={pathname === link.href ? "page" : undefined}
              >
                {link.label}
              </Link>
            ))}
          </div>

          {/* Right Section - Language & Contact */}
          <div className="flex items-center space-x-4">
            {/* Language Selector */}
            <div className="hidden lg:flex items-center space-x-2">
              <span className="text-white text-sm">🌐</span>
              <button
                onClick={toggleLanguage}
                className="text-white text-sm font-medium hover:text-[#D4AF37] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-black/80 rounded px-2 py-1"
              >
                {language}
              </button>
            </div>

            {/* Contact Us Button */}
            <Link
              href="/reserve"
              className="hidden lg:inline-flex items-center bg-[#D4AF37] text-white text-sm font-semibold px-6 py-2 rounded-md transition-all duration-200 hover:bg-[#D4AF37]/90 hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-black/80"
            >
              Reservation table{" "}
            </Link>

            {/* Mobile menu button */}
            <button
              onClick={toggleMenu}
              className="lg:hidden relative p-2 text-white hover:text-[#D4AF37] transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-[#D4AF37] focus:ring-offset-2 focus:ring-offset-black/80 rounded-md"
              aria-label={isMenuOpen ? "Close menu" : "Open menu"}
              aria-expanded={isMenuOpen}
              aria-controls="mobile-menu"
            >
              <span className="sr-only">
                {isMenuOpen ? "Close menu" : "Open menu"}
              </span>
              <div className="w-6 h-6 relative">
                <span
                  className={`absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out ${
                    isMenuOpen ? "rotate-45 translate-y-0" : "-translate-y-2"
                  }`}
                />
                <span
                  className={`absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out ${
                    isMenuOpen ? "opacity-0" : "opacity-100"
                  }`}
                />
                <span
                  className={`absolute block h-0.5 w-6 bg-current transform transition-all duration-300 ease-in-out ${
                    isMenuOpen ? "-rotate-45 translate-y-0" : "translate-y-2"
                  }`}
                />
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu Component */}
      <MobileMenu
        isOpen={isMenuOpen}
        onClose={handleMobileMenuClose}
        navLinks={navLinks}
        language={language}
        onLanguageChange={toggleLanguage}
      />
    </nav>
  );
};
