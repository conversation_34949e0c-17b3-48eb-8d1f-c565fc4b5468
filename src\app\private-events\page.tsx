"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { Button } from "@/components/ui/Button";
import { FiUsers, FiCalendar, FiStar } from "react-icons/fi";

export default function PrivateEventsPage() {
  const eventTypes = [
    {
      icon: FiUsers,
      title: "Corporate Events",
      description: "Professional meetings, team building, and corporate celebrations in an elegant setting.",
      capacity: "10-50 guests"
    },
    {
      icon: FiCalendar,
      title: "Special Occasions",
      description: "Birthdays, anniversaries, and milestone celebrations with personalized service.",
      capacity: "8-40 guests"
    },
    {
      icon: FiStar,
      title: "Wedding Parties",
      description: "Intimate wedding receptions and rehearsal dinners with custom menus.",
      capacity: "20-80 guests"
    }
  ];

  return (
    <>
      <Header />
      <main className="pt-20">
        {/* Hero Section */}
        <section className="py-20 px-4 bg-[#191919]">
          <div className="container mx-auto text-center">
            <motion.h1 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl md:text-5xl lg:text-6xl font-playfair font-bold text-white mb-6"
            >
              Private Events
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-gray-300 text-lg md:text-xl max-w-3xl mx-auto mb-12"
            >
              Create unforgettable memories with our exclusive private dining experiences. 
              Perfect for celebrations, corporate events, and special occasions.
            </motion.p>
          </div>
        </section>

        {/* Event Types */}
        <section className="py-20 px-4 bg-[#F5F5F0]">
          <div className="container mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
              {eventTypes.map((event, index) => (
                <motion.div
                  key={event.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white rounded-lg shadow-lg p-6 text-center"
                >
                  <event.icon className="text-[#D4AF37] text-3xl mx-auto mb-4" />
                  <h3 className="text-xl font-playfair font-bold text-[#2C2C2C] mb-3">
                    {event.title}
                  </h3>
                  <p className="text-[#666666] mb-4">{event.description}</p>
                  <div className="text-[#D4AF37] font-medium">{event.capacity}</div>
                </motion.div>
              ))}
            </div>

            {/* Features Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6 }}
                viewport={{ once: true }}
                className="relative"
              >
                <div className="border-2 border-[#D4AF37] p-3 bg-white shadow-lg">
                  <div className="relative aspect-[4/3] overflow-hidden">
                    <Image
                      src="/restaurant-interior.jpg"
                      alt="Private Dining Room"
                      fill
                      sizes="(max-width: 768px) 100vw, 50vw"
                      className="object-cover"
                    />
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, x: 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                viewport={{ once: true }}
                className="space-y-6"
              >
                <h2 className="text-3xl md:text-4xl font-playfair font-bold text-[#2C2C2C]">
                  Exceptional Private Dining
                </h2>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-[#D4AF37] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p className="text-[#666666]">Dedicated event coordinator to ensure every detail is perfect</p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-[#D4AF37] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p className="text-[#666666]">Customizable menus featuring our signature dishes</p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-[#D4AF37] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p className="text-[#666666]">Private dining rooms with elegant ambiance</p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-2 h-2 bg-[#D4AF37] rounded-full mt-2 mr-3 flex-shrink-0"></div>
                    <p className="text-[#666666]">Professional service staff dedicated to your event</p>
                  </div>
                </div>
                <Button
                  variant="primary"
                  size="lg"
                  className="bg-[#D4AF37] text-white hover:bg-[#D4AF37]/90 px-8 py-4"
                >
                  Request Information
                </Button>
              </motion.div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}
