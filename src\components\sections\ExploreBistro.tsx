"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import { Button } from "../ui/Button";

export const ExploreBistro = () => {
  const features = [
    {
      icon: "🌿",
      title: "Seasonal Ingredients",
      description: "Fresh, locally sourced ingredients"
    },
    {
      icon: "🍽️", 
      title: "Warm Service",
      description: "Exceptional hospitality experience"
    },
    {
      icon: "✨",
      title: "Luxurious Atmosphere", 
      description: "Elegant and sophisticated ambiance"
    }
  ];

  const galleryImages = [
    {
      src: "/assets/dishes/risotto-ai-funghi.jpg",
      alt: "Signature Risotto Dish"
    },
    {
      src: "/assets/dishes/veal-osso-bucco-cropped.jpg", 
      alt: "Grilled Specialties"
    },
    {
      src: "/assets/dishes/sea-bass-1.webp",
      alt: "Gourmet Presentation"
    },
    {
      src: "/assets/gallery04.png",
      alt: "Restaurant Interior"
    }
  ];

  return (
    <section className="py-20 px-4 bg-[#F5F5F0]">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-20 items-center">
          {/* Content Section - Left */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="space-y-8"
          >
            {/* Header */}
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-[#D4AF37] font-medium text-lg tracking-wide"
            >
              Our Restaurant&apos;s
            </motion.p>

            {/* Main Heading */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-3xl md:text-4xl lg:text-5xl font-playfair font-bold text-[#2C2C2C] leading-tight"
            >
              Explore Barnea Bistro
            </motion.h2>

            {/* Description */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-[#666666] text-base md:text-lg leading-relaxed max-w-lg"
            >
              Emphasizing our fusion of seasonal ingredients, warm service, and 
              sensuous design, Barnea Bistro boasts an open kitchen, a luxurious yet 
              brasserie-style atmosphere, and a prime location in Midtown Manhattan.
            </motion.p>

            {/* CTA Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="pt-4"
            >
              <Button
                variant="primary"
                size="lg"
                className="bg-[#D4AF37] text-white hover:bg-[#D4AF37]/90 font-semibold tracking-wide px-8 py-4 shadow-lg"
              >
                BARNEA BISTRO
              </Button>
            </motion.div>

            {/* Features */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              viewport={{ once: true }}
              className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8"
            >
              {features.map((feature, index) => (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                  viewport={{ once: true }}
                  className="text-center"
                >
                  <div className="text-2xl mb-2">{feature.icon}</div>
                  <h4 className="text-[#D4AF37] font-medium text-sm mb-1">
                    {feature.title}
                  </h4>
                  <p className="text-[#666666] text-xs">
                    {feature.description}
                  </p>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Image Gallery Section - Right */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="relative"
          >
            <div className="grid grid-cols-2 gap-4">
              {galleryImages.map((image, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.9 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.3 + index * 0.1 }}
                  viewport={{ once: true }}
                  className="relative aspect-square overflow-hidden rounded-lg shadow-lg"
                >
                  <Image
                    src={image.src}
                    alt={image.alt}
                    fill
                    sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 20vw"
                    className="object-cover hover:scale-105 transition-transform duration-300"
                    quality={90}
                  />
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};
