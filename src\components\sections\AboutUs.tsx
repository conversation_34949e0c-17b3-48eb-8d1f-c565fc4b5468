"use client";

import { motion } from "framer-motion";
import Image from "next/image";
import { Button } from "../ui/Button";

export const AboutUs = () => {
  return (
    <section className="py-20 px-4 bg-[#F5F5F0]">
      <div className="container mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-20 items-center">
          {/* Image Gallery Section */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="relative max-w-md mx-auto lg:mx-0"
          >
            {/* Main Restaurant Interior Image - Portrait orientation */}
            <div className="relative">
              <div className="border-2 border-[#D4AF37] p-3 bg-white shadow-lg">
                <div className="relative aspect-[3/4] overflow-hidden">
                  <Image
                    src="/assets/gallery03.png"
                    alt="Restaurant Interior"
                    fill
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 400px"
                    className="object-cover"
                    priority
                  />
                </div>
              </div>
            </div>

            {/* Overlapping Food Image - Bottom right */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="absolute -bottom-6 -right-6 md:-bottom-8 md:-right-8"
            >
              <div className="border-2 border-[#D4AF37] p-2 bg-white shadow-lg">
                <div className="relative w-36 h-36 md:w-44 md:h-44 overflow-hidden">
                  <Image
                    src="/assets/gallery04.png"
                    alt="Delicious Food"
                    fill
                    sizes="(max-width: 768px) 144px, 176px"
                    className="object-cover"
                  />
                </div>
              </div>
            </motion.div>
          </motion.div>

          {/* Content Section */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="space-y-8 lg:pl-8"
          >
            {/* About Us Header */}
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="text-[#D4AF37] font-medium text-lg tracking-wide"
            >
              About Us
            </motion.p>

            {/* Main Heading */}
            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-3xl md:text-4xl lg:text-5xl font-playfair font-bold text-[#2C2C2C] leading-tight"
            >
              About the Barnea Group
            </motion.h2>

            {/* Description Paragraphs */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <p className="text-[#666666] text-base md:text-lg leading-relaxed">
                With a strong focus on innovation, quality, and client satisfaction, we work
                tirelessly to meet and exceed expectations. Learn more about how our
                expertise can help you achieve your goals.
              </p>

              <p className="text-[#666666] text-base md:text-lg leading-relaxed">
                We specialize in delivering tailored solutions that drive success and create
                lasting value for our clients. Discover how our expertise, dedication, and
                collaborative approach can help you achieve your goals.
              </p>
            </motion.div>

            {/* Read More Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.6 }}
              viewport={{ once: true }}
              className="pt-6"
            >
              <Button
                variant="primary"
                size="lg"
                className="bg-[#D4AF37] text-white hover:bg-[#D4AF37]/90 font-semibold tracking-wide px-10 py-4 shadow-lg"
              >
                READ MORE
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};
