'use client';

import { motion } from 'framer-motion';

interface BookingFormProps {
  formData: {
    restaurant: string;
    seats: string;
    date: string;
    time: string;
  };
  onFormChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  onSubmit: (e: React.FormEvent) => void;
}

export const BookingForm = ({ formData, onFormChange, onSubmit }: BookingFormProps) => {
  return (
    <motion.div 
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      className="bg-white p-8 rounded-lg shadow-2xl max-w-md w-full mx-auto lg:ml-auto z-10"
    >
      <h2 className="text-2xl font-bold text-gray-800 mb-6 font-serif">Booking a table</h2>
      
      <form className="space-y-5" onSubmit={onSubmit}>
        <div>
          <label htmlFor="restaurant" className="block text-sm font-medium text-gray-700 mb-2">
            Restaurant
          </label>
          <select
            id="restaurant"
            name="restaurant"
            value={formData.restaurant}
            onChange={onFormChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent"
            required
          >
            <option value="">Select a restaurant</option>
            <option value="main">Main Restaurant</option>
            <option value="garden">Garden View</option>
            <option value="rooftop">Rooftop Lounge</option>
          </select>
        </div>

        <div>
          <label htmlFor="seats" className="block text-sm font-medium text-gray-700 mb-2">
            Total Seats
          </label>
          <input
            type="number"
            id="seats"
            name="seats"
            min="1"
            max="20"
            value={formData.seats}
            onChange={onFormChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent"
            placeholder="Number of people"
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-2">
              Date
            </label>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={onFormChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent"
              required
            />
          </div>
          <div>
            <label htmlFor="time" className="block text-sm font-medium text-gray-700 mb-2">
              Time
            </label>
            <input
              type="time"
              id="time"
              name="time"
              value={formData.time}
              onChange={onFormChange}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-amber-400 focus:border-transparent"
              required
            />
          </div>
        </div>

        <motion.button
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          type="submit"
          className="w-full bg-amber-500 hover:bg-amber-600 text-white font-medium py-3 px-4 rounded-lg transition-all hover:shadow-lg"
        >
          Book Now
        </motion.button>
      </form>
    </motion.div>
  );
};
