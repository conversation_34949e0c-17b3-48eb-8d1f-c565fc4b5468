'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import { Button } from '../ui/Button';

interface NewMenuFeatureProps {
  imagePosition?: 'left' | 'right';
}

export const NewMenuFeature = ({ imagePosition = 'left' }: NewMenuFeatureProps) => {
  return (
    <section className="py-20 px-4">
      <div className="container mx-auto">
        <div className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
          imagePosition === 'right' ? 'lg:flex-row-reverse' : ''
        }`}>
          {/* Image Column */}
          <motion.div
            initial={{ opacity: 0, x: imagePosition === 'left' ? -20 : 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="relative aspect-square rounded-full overflow-hidden"
          >
            <Image
              src="/assets/dishes/Spicy-Rigatoni_THUMB.jpg"
              alt="Spicy Rigatoni Pasta"
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              className="object-cover"
              priority
            />
          </motion.div>

          {/* Content Column */}
          <motion.div
            initial={{ opacity: 0, x: imagePosition === 'left' ? 20 : -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className={`flex flex-col ${
              imagePosition === 'right' ? 'lg:items-end lg:text-right' : ''
            }`}
          >
            <h2 className="text-3xl md:text-4xl font-playfair font-bold mb-4">
              Spicy Rigatoni Pasta
              <br />
              <span className="text-[#FDB92A]">with Fresh Herbs</span>
            </h2>
            <p className="text-gray-400 mb-6 max-w-lg">
              Experience our signature spicy rigatoni, perfectly cooked al dente
              with a rich tomato sauce, fresh herbs, and a hint of chili.
              A true Italian classic with a modern twist.
            </p>
            <div className="flex items-center gap-6 mb-8">
              <span className="text-2xl font-bold text-[#FDB92A]">$25.00</span>
              <div className="h-8 w-px bg-gray-700" />
              <div className="flex items-center gap-1">
                <span className="text-yellow-400">★★★★★</span>
                <span className="text-gray-400">(4.9)</span>
              </div>
            </div>
            <Button variant="primary" size="lg">
              View Menu
            </Button>
          </motion.div>
        </div>
      </div>
    </section>
  );
};
