import { MenuItem, ServiceItem, TestimonialItem, Recipe } from '../lib/types';

export const MENU_ITEMS: MenuItem[] = [
  {
    id: 1,
    name: "Spicy Rigatoni",
    price: 25,
    image: "/assets/dishes/Spicy-Rigatoni_THUMB.jpg",
    description: "Authentic Italian rigatoni with spicy tomato sauce"
  },
  {
    id: 2,
    name: "Sea Bass Fillet",
    price: 32,
    image: "/assets/dishes/sea-bass-1.webp",
    description: "Fresh sea bass fillet with herbs and lemon butter sauce"
  },
  // Add more menu items here
];

export const SERVICES: ServiceItem[] = [
  {
    id: 1,
    title: "Online Order",
    description: "Easy and convenient online ordering system",
    icon: "shopping-cart"
  },
  {
    id: 2,
    title: "24/7 Service",
    description: "Available round the clock for your convenience",
    icon: "clock"
  },
  // Add more services here
];

export const FEATURED_RECIPE: Recipe = {
  title: "Chef's Special Risotto",
  ingredients: [
    "Arborio rice",
    "Fresh mushrooms",
    "Parmesan cheese",
    "White wine",
    "Truffle oil and herbs"
  ],
  image: "/assets/dishes/risotto-ai-funghi.jpg"
};

export const TESTIMONIALS: TestimonialItem[] = [
  {
    id: 1,
    name: "Chef Michael",
    image: "/assets/chef.png",
    comment: "The best food experience in town! Every dish is a masterpiece.",
    rating: 5
  },
  // Add more testimonials here
];

export const NAV_LINKS = [
  { href: "/", label: "Home" },
  { href: "/menu", label: "Menu" },
  { href: "/about", label: "About" },
  { href: "/contact", label: "Contact" }
];
