# Create Background Video from Existing Images

## Using FFmpeg to Create Video from Images

You can create a background video using the existing high-quality images in your assets folder.

### Step 1: Install FFmpeg
Download FFmpeg from: https://ffmpeg.org/download.html

### Step 2: Prepare Images
Use these existing images from your assets folder:
- `bg.png` (main restaurant interior)
- `gallery01.png` (food/ambiance)
- `gallery02.png` (restaurant atmosphere)
- `bistro-1.jpg` through `bistro-4.jpg` (bistro images)

### Step 3: Create Video with FFmpeg

#### Option A: Simple Slideshow (Recommended)
```bash
# Navigate to the assets directory
cd public/assets

# Create a slideshow video (each image shows for 3 seconds)
ffmpeg -framerate 1/3 -pattern_type glob -i "*.{png,jpg}" -c:v libx264 -r 30 -pix_fmt yuv420p restaurant-background.mp4
```

#### Option B: <PERSON> (Zoom/Pan)
```bash
# Create a more dynamic video with zoom and pan effects
ffmpeg -loop 1 -i bg.png -loop 1 -i gallery01.png -loop 1 -i gallery02.png -filter_complex "[0:v]scale=1920:1080,zoompan=z='min(zoom+0.0015,1.5)':d=125[v0];[1:v]scale=1920:1080,zoompan=z='min(zoom+0.0015,1.5)':d=125[v1];[2:v]scale=1920:1080,zoompan=z='min(zoom+0.0015,1.5)':d=125[v2];[v0][v1][v2]concat=n=3:v=1:a=0[out]" -map "[out]" -t 15 -c:v libx264 -r 30 -pix_fmt yuv420p restaurant-background.mp4
```

#### Option C: Crossfade Transitions
```bash
# Create smooth crossfade transitions between images
ffmpeg -loop 1 -i bg.png -loop 1 -i gallery01.png -loop 1 -i gallery02.png -filter_complex "[0:v][1:v]blend=all_expr='A*(if(gte(T,3),1,T/3))+B*(1-(if(gte(T,3),1,T/3)))':shortest=1:repeatlast=1[b1];[b1][2:v]blend=all_expr='A*(if(gte(T,3),1,T/3))+B*(1-(if(gte(T,3),1,T/3)))':shortest=1:repeatlast=1[out]" -map "[out]" -t 12 -c:v libx264 -r 30 -pix_fmt yuv420p restaurant-background.mp4
```

### Step 4: Create WebM Version (Optional)
```bash
# Convert MP4 to WebM for better compression
ffmpeg -i restaurant-background.mp4 -c:v libvpx-vp9 -crf 30 -b:v 0 restaurant-background.webm
```

### Step 5: Optimize for Web
```bash
# Create a smaller, web-optimized version
ffmpeg -i restaurant-background.mp4 -vf scale=1920:1080 -c:v libx264 -crf 28 -preset slow -movflags +faststart restaurant-background-optimized.mp4
```

## Alternative: Online Video Creation Tools

If you prefer not to use command line tools:

1. **Canva** (free/paid): Create video slideshows from images
2. **Adobe Express** (free): Simple video creation from images
3. **InVideo** (free tier): Online video editor
4. **Kapwing** (free tier): Browser-based video creation

## Recommended Settings for Restaurant Background Video

- **Duration**: 10-20 seconds (will loop automatically)
- **Resolution**: 1920x1080 (Full HD)
- **Frame Rate**: 30 fps
- **Format**: MP4 (H.264 codec)
- **File Size**: Under 5MB for good performance
- **Content**: Slow, subtle movements or transitions

## Quick Solution: Use Existing Static Image

If you want to test the component immediately, you can temporarily modify the Hero component to use a static background image instead of video by setting `videoError` to `true` by default.
